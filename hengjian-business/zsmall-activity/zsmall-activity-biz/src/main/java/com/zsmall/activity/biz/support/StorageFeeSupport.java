package com.zsmall.activity.biz.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hengjian.common.core.utils.DateUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.entity.domain.StorageFeeInfo;
import com.zsmall.activity.entity.domain.StorageFeeItem;
import com.zsmall.activity.entity.domain.TransactionStorageFee;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import com.zsmall.activity.entity.iservice.*;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.payment.PayTypeEnum;
import com.zsmall.common.enums.productActivity.ProductActivityStateEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.storageFee.FeeStateEnum;
import com.zsmall.common.enums.storageFee.PayStateEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.common.exception.OrderPayException;
import com.zsmall.common.exception.WalletException;
import com.zsmall.system.biz.service.TenantWalletService;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.domain.TenantWallet;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITransactionRecordService;
import com.zsmall.system.entity.mapper.TenantWalletMapper;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年7月9日  16:10
 * @description: 仓储费支持类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StorageFeeSupport {

    private final IDistributorProductActivityService distributorProductActivityService;
    private final IDistributorProductActivityStockService distributorProductActivityStockService;
    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;
    private final IStorageFeeItemService storageFeeItemService;
    private final IStorageFeeInfoService storageFeeInfoService;
    private final TenantWalletService tenantWalletService;
    private final MallSystemCodeGenerator mallSystemCodeGenerator;
    private final TenantWalletMapper tenantWalletMapper;
    private final ITransactionRecordService iTransactionRecordService;
    private final ITransactionStorageFeeService transactionStorageFeeService;

    /**
     * 生成仓储费
     */
    @InMethodLog("生成仓储费实现方法")
    public void storageFee() {
        // 获取全部分销商活动数据，去已发布和进行中的活动
        List<DistributorProductActivity> distributorProductActivityList = distributorProductActivityService.queryListByActivityState(Arrays.asList("Published", "InProgress"));
        List<StorageFeeItem> storageFeeItemList = new ArrayList<>();
        if (CollUtil.isEmpty(distributorProductActivityList)) {
            return;
        }
        for (DistributorProductActivity distributorProductActivity : distributorProductActivityList) {
            // 活动开始时间+免仓期 大于当前时间，才生成仓储费
            if (LocalDateTime.now()
                             .isAfter(DateUtil.offsetDay(distributorProductActivity.getActiveStartTime(), distributorProductActivity.getFreeStoragePeriod()
                                                                                                                                    .intValue())
                                              .toLocalDateTime())) {
                String distributorActivityCode = distributorProductActivity.getDistributorActivityCode();
                // 明细结算时间：显示分销商活动期间内，每一天的仓储费明细记录的结算时间；格式为“2025-06-06 23:59:59”
                String feeSettlement = StrUtil.format("{}235959", LocalDateTimeUtil.now()
                                                                                   .format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                // 当天的23:59:59
                String feeSettlementDate = LocalDateTimeUtil.now()
                                                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd 23:59:59"));
                // 分销商活动仓库
                List<DistributorProductActivityStock> distributorProductActivityStockList = distributorProductActivityStockService.queryListByDistributorProductActivityIds(Arrays.asList(distributorProductActivity.getId()));
                if (CollUtil.isEmpty(distributorProductActivityStockList)) {
                    continue;
                }
                for (DistributorProductActivityStock distributorProductActivityStock : distributorProductActivityStockList) {
                    StorageFeeItem storageFeeItem = new StorageFeeItem();
                    String warehouse = distributorProductActivityStock.getWarehouseCode();
                    String warehouseSystemCode = distributorProductActivityStock.getWarehouseSystemCode();
                    // 明细ID：生成规则为“分销商活动ID+仓库编号+明细结算时间”,明细结算时间是当天的最后一秒
                    String detailId = StrUtil.format("{}{}{}", distributorActivityCode, warehouse, feeSettlement);
                    Long skuNum = Long.valueOf(distributorProductActivityStock.getQuantityTotal());
                    Long skuRemainingNum = Long.valueOf(distributorProductActivityStock.getQuantitySurplus());
                    BigDecimal storageFee = distributorProductActivity.getDistributorActivityStorageFee();
                    String currencyCode = null;
                    String currencySymbol = null;
                    if (StringUtils.isNotBlank(distributorProductActivity.getSite())) {
                        LambdaQueryWrapper<SiteCountryCurrency> q = new LambdaQueryWrapper<>();
                        q.eq(SiteCountryCurrency::getCountryCode, distributorProductActivity.getSite());
                        SiteCountryCurrency siteCountryCurrency = iSiteCountryCurrencyService.getBaseMapper()
                                                                                             .selectOne(q);
                        if (ObjectUtil.isNotNull(siteCountryCurrency)) {
                            currencySymbol = siteCountryCurrency.getCurrencySymbol();
                            currencyCode = siteCountryCurrency.getCurrencyCode();
                        }
                    }
                    BigDecimal storageFeeDay = BigDecimal.ZERO;
                    if (null != skuRemainingNum && null != storageFee) {
                        storageFeeDay = new BigDecimal(skuRemainingNum).multiply(storageFee);
                    }
                    String sku = distributorProductActivity.getProductSku();
                    String skuId = distributorProductActivity.getProductSkuCode();
                    String site = distributorProductActivity.getSite();
                    String activityId = distributorActivityCode;
                    String tenantId = distributorProductActivity.getDistributorTenantId();
                    storageFeeItem.setTenantId(tenantId).setDetailId(detailId).setWarehouseCode(warehouse)
                                  .setWarehouseSystemCode(warehouseSystemCode).setSkuNum(skuNum)
                                  .setSkuRemainingNum(skuRemainingNum).setStorageFee(storageFee)
                                  .setStorageFeeDay(storageFeeDay).setSkuId(skuId).setSku(sku)
                                  .setCurrencyCode(currencyCode).setCurrencySymbol(currencySymbol).setSite(site)
                                  .setActivityId(activityId).setFeeSettlementDate(feeSettlementDate);
                    storageFeeItemList.add(storageFeeItem);
                }
            }
        }
        if (CollUtil.isNotEmpty(storageFeeItemList)) {
            storageFeeItemService.saveBatch(storageFeeItemList);
        }
    }

    /**
     * 生成主仓储费，可通过传参来进行指定分销商活动生成仓储费
     *
     * @param distributorActivityCode 分销商活动ID
     */
    @InMethodLog("生成主仓储费实现方法")
    @Transactional(rollbackFor = Exception.class)
    public void storageFeeMain(String distributorActivityCode) {
        List<StorageFeeInfo> storageFeeInfoList = new ArrayList<>();
        List<DistributorProductActivity> distributorProductActivityList;
        // 已完成、已过期、已取消的活动生成仓储费
        List<String> activityStateList = Arrays.asList(ProductActivityStateEnum.Completed.getValue(), ProductActivityStateEnum.Expired.getValue(), ProductActivityStateEnum.Canceled.getValue());
        if (StringUtils.isNotEmpty(distributorActivityCode)) {
            distributorProductActivityList = distributorProductActivityService.list(new LambdaQueryWrapper<DistributorProductActivity>().eq(DistributorProductActivity::getDistributorActivityCode, distributorActivityCode));
        } else {
            distributorProductActivityList = distributorProductActivityService.list(new LambdaQueryWrapper<DistributorProductActivity>().in(DistributorProductActivity::getActivityState, activityStateList));
        }
        if (null == distributorProductActivityList) {
            return;
        }
        // 判断活动是否已经生成过仓储费
        List<StorageFeeInfo> storageFeeInfoListExist = storageFeeInfoService.list(new LambdaQueryWrapper<StorageFeeInfo>().eq(StorageFeeInfo::getActivityId, distributorProductActivityList.stream()
                                                                                                                                                                                           .map(DistributorProductActivity::getDistributorActivityCode)
                                                                                                                                                                                           .collect(Collectors.toList())));
        if (CollUtil.isNotEmpty(storageFeeInfoListExist)) {
            // 去除已经生成过仓储费的活动
            distributorProductActivityList.removeIf(distributorProductActivity -> storageFeeInfoListExist.stream()
                                                                                                         .anyMatch(storageFeeInfo -> storageFeeInfo.getActivityId()
                                                                                                                                                   .equals(distributorProductActivity.getDistributorActivityCode())));
            if (CollUtil.isEmpty(distributorProductActivityList)) {
                return;
            }
        }

        // 状态判断，只有已完成和已过期的活动生成主仓储费
        for (DistributorProductActivity distributorProductActivity : distributorProductActivityList) {
            if (StringUtils.isNotEmpty(distributorProductActivity.getDistributorActivityCode())) {
                List<StorageFeeItem> storageFeeItemList = storageFeeItemService.list(new LambdaQueryWrapper<StorageFeeItem>().eq(StorageFeeItem::getActivityId, distributorProductActivity.getDistributorActivityCode())
                                                                                                                             .isNull(StorageFeeItem::getStorageFeeId));
                if (CollUtil.isEmpty(storageFeeItemList)) {
                    continue;
                }
                StorageFeeInfo storageFee = new StorageFeeInfo();
                String tenantId = distributorProductActivity.getDistributorTenantId();
                String currencyCode = null;
                String currencySymbol = null;
                if (StringUtils.isNotBlank(distributorProductActivity.getSite())) {
                    LambdaQueryWrapper<SiteCountryCurrency> q = new LambdaQueryWrapper<>();
                    q.eq(SiteCountryCurrency::getCountryCode, distributorProductActivity.getSite());
                    SiteCountryCurrency siteCountryCurrency = iSiteCountryCurrencyService.getBaseMapper()
                                                                                         .selectOne(q);
                    if (ObjectUtil.isNotNull(siteCountryCurrency)) {
                        currencySymbol = siteCountryCurrency.getCurrencySymbol();
                        currencyCode = siteCountryCurrency.getCurrencyCode();
                    }
                }
                String activityId = distributorProductActivity.getDistributorActivityCode();
                String storageFeeSettlementDate = LocalDateTimeUtil.now()
                                                                   .format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS));
                Integer feeState = FeeStateEnum.WAIT_CONFIRM.getValue();
                String activityType = distributorProductActivity.getActivityType();
                String logisticsType = distributorProductActivity.getSupportedLogistics();
                Integer payState = PayStateEnum.UNPAID.getValue();
                String storageFeeId = StrUtil.format("{}{}total{}", tenantId, currencyCode, activityId);
                BigDecimal totalStorageFee = storageFeeItemList.stream().map(StorageFeeItem::getStorageFeeDay)
                                                               .reduce(BigDecimal.ZERO, BigDecimal::add);
                storageFee.setTenantId(tenantId).setStorageFeeId(storageFeeId).setFeeState(feeState)
                          .setCurrencyCode(currencyCode).setCurrencySymbol(currencySymbol)
                          .setTotalStorageFee(totalStorageFee)
                          .setStorageFeeSettlementDate(storageFeeSettlementDate).setActivityId(activityId)
                          .setActivityType(activityType).setLogisticsType(logisticsType).setPayState(payState);
                storageFeeInfoList.add(storageFee);
                // storageFeeItemList 添加storageFeeId
                for (StorageFeeItem storageFeeItem : storageFeeItemList) {
                    storageFeeItem.setStorageFeeId(storageFeeId);
                }
                storageFeeItemService.updateBatchById(storageFeeItemList);
            }
        }
        if (CollUtil.isNotEmpty(storageFeeInfoList)) {
            storageFeeInfoService.saveBatch(storageFeeInfoList);
        }
    }

    @InMethodLog("仓储费钱包支付")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void storageFeePay(List<StorageFeeInfo> storageFeeInfoList) throws Exception {
        if (CollUtil.isEmpty(storageFeeInfoList)) {
            return;
        }
        for (StorageFeeInfo storageFeeInfo : storageFeeInfoList) {
            try {
                TransactionRecord transactionRecord = new TransactionRecord(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo));
                transactionRecord.setTransactionType(TransactionTypeEnum.Expenditure);
                transactionRecord.setTransactionSubType(TransactionSubTypeEnum.ProductActivityStorageFee);
                transactionRecord.setCurrency(storageFeeInfo.getCurrencyCode());
                transactionRecord.setCurrencySymbol(storageFeeInfo.getCurrencySymbol());
                // 分销平台内实际交易金额
                transactionRecord.setTransactionAmount(storageFeeInfo.getTotalStorageFee());
                LambdaQueryWrapper<TenantWallet> twLqw = new LambdaQueryWrapper<>();
                String tenantId = storageFeeInfo.getTenantId();
                twLqw.eq(TenantWallet::getTenantId, tenantId);
                if (StringUtils.isNotBlank(storageFeeInfo.getCurrencyCode())) {
                    twLqw.eq(TenantWallet::getCurrency, storageFeeInfo.getCurrencyCode());
                }
                TenantWallet tenantWallet = tenantWalletMapper.selectOne(twLqw);
                BigDecimal balance = BigDecimal.ZERO;
                if (ObjectUtil.isNotNull(tenantWallet)) {
                    balance = tenantWallet.getWalletBalance();
                }
                transactionRecord.setBeforeBalance(balance);
                transactionRecord.setAfterBalance(balance.subtract(storageFeeInfo.getTotalStorageFee()));
                transactionRecord.setTransactionState(TransactionStateEnum.Processing);
                transactionRecord.setTransactionTime(new Date());
                iTransactionRecordService.save(transactionRecord);
                // 保存交易记录和仓储费之间的关系
                TransactionStorageFee transactionStorageFee = new TransactionStorageFee().setStorageFeeId(storageFeeInfo.getId()).setTransactionsId(transactionRecord.getId()).setCreateTime(LocalDateTime.now());
                TenantHelper.ignore(() -> transactionStorageFeeService.save(transactionStorageFee));
                tenantWalletService.walletChanges(transactionRecord);
            } catch (WalletException e) {
                log.info("仓储费{}钱包支付时出现异常（WalletException） {}", storageFeeInfo, e.getMessage(), e);
                LocaleMessage localeMessage = e.getLocaleMessage();
                storageFeeInfo.setPayState(PayStateEnum.FAILED.getValue());
                storageFeeInfo.setErrorMessage(localeMessage.toJSON());
                storageFeeInfoService.updateById(storageFeeInfo);
            } finally {
                // 修改交易记录
                List<TransactionStorageFee> transactionStorageFeeList = TenantHelper.ignore(() -> transactionStorageFeeService.list(new LambdaQueryWrapper<TransactionStorageFee>().eq(TransactionStorageFee::getStorageFeeId, storageFeeInfo.getId())));
                if (CollUtil.isNotEmpty(transactionStorageFeeList)) {
                    // 获取全部的transactinosId
                    List<Long> transactionIds = transactionStorageFeeList.stream().map(TransactionStorageFee::getTransactionsId).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(transactionIds)) {
                        iTransactionRecordService.update(new TransactionRecord(), new LambdaUpdateWrapper<TransactionRecord>().in(TransactionRecord::getId, transactionIds).eq(TransactionRecord::getTransactionState, TransactionStateEnum.Processing));
                    }
                }
            }
        }
    }

    /**
     * 中空云汇支付仓储费回调处理
     * @param storageFeeInfoList
     * @param isSuccess
     * @param payload
     */
    @InMethodLog("第三方支付仓储费回调处理")
    public void storageFeePayCallback(List<StorageFeeInfo> storageFeeInfoList,Boolean isSuccess,String payload,Integer payType) {
        // 交易记录生成
        for(StorageFeeInfo storageFeeInfo : storageFeeInfoList){
            TransactionRecord transactionRecord = new TransactionRecord(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo));
            transactionRecord.setTransactionType(TransactionTypeEnum.Expenditure);
            transactionRecord.setTransactionSubType(TransactionSubTypeEnum.ProductActivityStorageFee);
            transactionRecord.setCurrency(storageFeeInfo.getCurrencyCode());
            transactionRecord.setCurrencySymbol(storageFeeInfo.getCurrencySymbol());
            // 分销平台内实际交易金额
            transactionRecord.setTransactionAmount(storageFeeInfo.getTotalStorageFee());
            transactionRecord.setTransactionState(TransactionStateEnum.Processing);
            transactionRecord.setTransactionTime(new Date());
            if(null != payType){
                if(payType.equals(PayTypeEnum.Airwallex_Storage_Fee.getValue())){
                    transactionRecord.setTransactionNote("空中云汇收单支付仓储费");
                }
                if(payType.equals(PayTypeEnum.Payoneer_Storage_Fee.getValue())){
                    transactionRecord.setTransactionNote("payoneer收单支付仓储费");
                }
            }else {
                transactionRecord.setTransactionNote("支付仓储费");
            }
            iTransactionRecordService.save(transactionRecord);
            // 保存交易记录和仓储费之间的关系
            TransactionStorageFee transactionStorageFee = new TransactionStorageFee().setStorageFeeId(storageFeeInfo.getId()).setTransactionsId(transactionRecord.getId()).setCreateTime(LocalDateTime.now());
            TenantHelper.ignore(() -> transactionStorageFeeService.save(transactionStorageFee));
        }
        storageFeeInfoList.forEach(storageFeeInfo -> storageFeeInfo.setPayState(PayStateEnum.PAID.getValue()));
        storageFeeInfoList.forEach(storageFeeInfo -> storageFeeInfo.setFeeState(FeeStateEnum.CONFIRMED.getValue()));
        storageFeeInfoService.updateBatchById(storageFeeInfoList);
        // 交易记录处理，从storageFeeInfoList 获取id组成集合
        List<Long> storageFeeIdList = storageFeeInfoList.stream().map(StorageFeeInfo::getId).collect(Collectors.toList());
        List<TransactionStorageFee> transactionStorageFeeList = TenantHelper.ignore(() -> transactionStorageFeeService.list(new LambdaQueryWrapper<TransactionStorageFee>().in(TransactionStorageFee::getStorageFeeId, storageFeeIdList)));
        if(CollUtil.isNotEmpty(transactionStorageFeeList)){
            List<TransactionRecord> transactionRecordList = iTransactionRecordService.list(new LambdaQueryWrapper<TransactionRecord>().in(TransactionRecord::getId, transactionStorageFeeList.stream()
                                                                                                                                                                                             .map(TransactionStorageFee::getTransactionsId)
                                                                                                                                                                                             .collect(Collectors.toList())));
            if(CollUtil.isEmpty(transactionRecordList)){
                log.error("根据storageFeeIdList获取交易记录失败{}", transactionStorageFeeList);
                return;
            }
            if(isSuccess){
                transactionRecordList.forEach(transactionRecord -> transactionRecord.setTransactionState(TransactionStateEnum.Success));
            }else {
                cn.hutool.json.JSONObject message = new cn.hutool.json.JSONObject();
                if(null != payType){
                    if(payType.equals(PayTypeEnum.Airwallex_Storage_Fee.getValue())){
                        message = JSONUtil.createObj().putOnce("message", "空中云汇支付失败" + payload);
                    }
                    if(payType.equals(PayTypeEnum.Payoneer_Storage_Fee.getValue())){
                        message = JSONUtil.createObj().putOnce("message", "payoneer支付失败" + payload);
                    }
                }else {
                    message = JSONUtil.createObj().putOnce("message", "第三方支付失败" + payload);
                }
                for (TransactionRecord transactionRecord : transactionRecordList){
                    transactionRecord.setTransactionState(TransactionStateEnum.Failure);
                    transactionRecord.setFailureReason(message);
                }
            }
            iTransactionRecordService.updateBatchById(transactionRecordList);
        }

    }

}
