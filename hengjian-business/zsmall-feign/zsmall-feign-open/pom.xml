<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>zsmall-feign</artifactId>
        <groupId>com.zsmall</groupId>
        <version>${zsmall.version}</version>
    </parent>

    <artifactId>zsmall-feign-open</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall.extend.wms</groupId>
            <artifactId>zsmall-wms-thebizark</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-warehouse-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-product-entity</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-system</artifactId>
        </dependency>
    </dependencies>
    <groupId>com.zsmall</groupId>
    <name>ZS-Mall外部业务数据模块</name>
    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>
