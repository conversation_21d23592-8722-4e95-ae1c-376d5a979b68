package com.zsmall.warehouse.service;


import com.hengjian.common.core.domain.R;
import com.zsmall.warehouse.entity.domain.dto.WarehouseInventoryDTO;
import com.zsmall.warehouse.entity.domain.vo.WarehouseInventoryVo;

import javax.servlet.http.HttpServletResponse;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/4/29 14:42
 */
public interface WarehouseInventoryService {
    R<WarehouseInventoryVo> getInventoryPlan(WarehouseInventoryDTO dto) throws Exception;

    void inventoryPlanExport(WarehouseInventoryDTO dto, HttpServletResponse response);

    R<Boolean> isVisibleDistributorConfiguration();

    R<Boolean> isVisibleSupplierConfiguration();

}
